<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use App\Http\Controllers\Api\EmailsController;

class Bag extends Model
{
    public $guarded = [];

    public $casts = [
        'meta' => 'array',
    ];

    public $table = 'bag';

    public function model()
    {
        return $this->morphTo();
    }

    public function getPrice()
    {
        return $this->model->price * $this->quantity;
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }
    // public function savings($discount){
    //     $product=$this->model->product?$this->model->product:$this->model;
    //     if($product->applyDiscount($discount)){
    //         $percentage=$discount->amount/100;
    //         return number_format($this->getPrice()*$percentage,2);
    //     }else{
    //         return 0;
    //     }
    // }
    public function getFrontEndAttribute($quantity = 1)
    {
        if ($model = $this->model) {
            return array_merge(
                $model->getFrontEndAttribute($quantity),
                $this->meta ?? [],
                [
                    'item_key' => $this->item_key
                ]
            );
        } elseif ($this->model_type == 'App\GiftCard') {
            return array_merge(
                (new GiftCard)->getFrontEndAttribute($this->meta),
                ['item_key' => $this->item_key]
            );
        }
    }

    public function getWeightAttribute()
    {
        if ($this->model_type != 'App\\GiftCard') {
            return $this->model->weight * $this->quantity;
        }
    }

    public function getShippableAttribute()
    {
        return $this->model->shippable;
        if ($this->model_type == 'App\\Bundle') {
        }
        return data_get($this, 'model.item_type') == 'physical' || data_get($this, 'model.item_type') == 'both';
    }

    public function getPriceAttribute()
    {
        if ($this->model_type == 'App\\GiftCard') {
            return $this->meta['price'] * $this->quantity;
        }
        return $this->model->price * $this->quantity;
    }

    public function getBillableWeightAttribute()
    {
        if (!$product = $this->model) {
            return null;
        }
        return $product->billableWeight;
        // if ($product->height && $product->width && $product->length) {
        //     $cubic_size = $product->height * $product->width * $product->length;
        //     $dimensional_weight = $cubic_size / (settings()->getValue('dimensional_factor') ?? 139);
        // }
        // return max([$dimensional_weight ?? 0, $product->weight]);
    }

    public static function SendAbandonedEmails()
    {
        $customerIds = self::pluck('customer_id')->unique();
        $customerIds->each(function ($id) {
            dispatch(function () use ($id) {
                $customer = Customer::find($id);
                if ($customer->abandoned()) {
                    EmailsController::SendAbandonedCart($customer);
                    $customer->setBagsAsReminded();
                }
            });
        });
    }

    public function getPersonalizationsTotal()
    {
        $product = $this->model;
        if ($product && $product->personalizes) {
            return $product->personalizes->duration;
        }
    }

    public function getPersonalizationsDuration()
    {
        $product = $this->model;
        if ($product && $product->personalizes) {
            return $product->personalizes->duration;
        }
    }

    public function getExtendedDuration()
    {
        return optional($this->model)->getExtendedDuration();
        //     $extendedDurtion = 0;
        //     if($this->model_type == 'App\Bundle') {

        //         $bundle = $this->model;

        //         $bundle->bundleItems->each(function($item) use(&$extendedDurtion) {

        //             $product = $this->model;
        //             $extendedDurtion += $product->duration ? ($product->duration + 1) : 0;
        //             $extendedDurtion += now()->isBefore($product->release_date) ? (now()->diffInDays($product->release_date) + 1) : 0;

        //         });

        //     } else if ($this->model_type != 'App\GiftCard') {
        //         $product = $this->model;
        //         $extendedDurtion += $product->duration ? ($product->duration + 1) : 0;
        //         $extendedDurtion += now()->isBefore($product->release_date) ? (now()->diffInDays($product->release_date) + 1) : 0;
        //     }
        //    return $extendedDurtion;
    }
}
